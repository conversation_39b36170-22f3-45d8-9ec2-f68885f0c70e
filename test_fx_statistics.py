#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普法工作管理系统统计接口测试脚本
"""

import os
import sys
import requests
import json
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 测试配置
BASE_URL = "http://localhost:5000"
TEST_USERNAME = "admin"  # 根据实际情况修改
TEST_PASSWORD = "admin123"  # 根据实际情况修改


def login():
    """登录获取token"""
    login_url = f"{BASE_URL}/login"
    login_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 200:
                token = result.get('token')
                print(f"登录成功，获取到token: {token[:20]}...")
                return token
            else:
                print(f"登录失败: {result.get('msg')}")
                return None
        else:
            print(f"登录请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"登录异常: {str(e)}")
        return None


def test_info_summary(token):
    """测试信息汇总统计接口"""
    print("\n=== 测试信息汇总统计接口 ===")
    
    url = f"{BASE_URL}/api/faxuan/statistics/info_summary"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")


def test_info_detail(token):
    """测试信息详细统计接口"""
    print("\n=== 测试信息详细统计接口 ===")
    
    # 测试不带参数的请求
    url = f"{BASE_URL}/api/faxuan/statistics/info_detail"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")
    
    # 测试带参数的请求
    print("\n--- 测试带参数的详细统计 ---")
    params = {
        "date": "2025-08-05",
        "channel": "微信公众号"
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")


def test_info_trend(token):
    """测试信息趋势统计接口"""
    print("\n=== 测试信息趋势统计接口 ===")
    
    url = f"{BASE_URL}/api/faxuan/statistics/trend"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试默认7天趋势
    try:
        response = requests.get(url, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")
    
    # 测试30天趋势
    print("\n--- 测试30天趋势统计 ---")
    params = {"days": 30}
    
    try:
        response = requests.get(url, headers=headers, params=params)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"请求失败: {response.text}")
    except Exception as e:
        print(f"请求异常: {str(e)}")


def test_database_connection():
    """测试数据库连接和数据"""
    print("\n=== 测试数据库连接 ===")
    
    try:
        from web import create_app, db
        from web.models.faxuan.FXArticleRecords import FxArticleRecords
        
        app = create_app(os.getenv('FLASK_CONFIG') or 'dev')
        with app.app_context():
            # 测试查询文章记录数量
            total_count = db.session.query(FxArticleRecords).count()
            print(f"数据库中文章记录总数: {total_count}")
            
            # 测试查询今日新增
            today = date.today()
            today_start = datetime.combine(today, datetime.min.time())
            today_end = datetime.combine(today, datetime.max.time())
            
            today_count = db.session.query(FxArticleRecords).filter(
                FxArticleRecords.create_time >= today_start,
                FxArticleRecords.create_time <= today_end
            ).count()
            print(f"今日新增文章数: {today_count}")
            
            # 测试查询渠道分布
            from sqlalchemy import func
            channel_stats = db.session.query(
                FxArticleRecords.crawl_channel,
                func.count(FxArticleRecords.id).label('count')
            ).group_by(FxArticleRecords.crawl_channel).all()
            
            print("渠道分布:")
            for stat in channel_stats:
                print(f"  {stat.crawl_channel}: {stat.count}")
                
    except Exception as e:
        print(f"数据库测试异常: {str(e)}")


def main():
    """主测试函数"""
    print("开始测试普法工作管理系统统计接口")
    print("=" * 50)
    
    # 首先测试数据库连接
    test_database_connection()
    
    # 登录获取token
    token = login()
    if not token:
        print("无法获取token，跳过接口测试")
        return
    
    # 测试各个接口
    test_info_summary(token)
    test_info_detail(token)
    test_info_trend(token)
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
