# 普法工作管理系统统计相关数据模型
# 用于统计平台收集官方信息的相关数据结构

from datetime import datetime
from web import db


class FxInfoStatistics(db.Model):
    """
    普法信息统计表
    用于存储每日统计数据，提高查询性能
    """
    __tablename__ = 'fx_info_statistics'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    stat_date = db.Column(db.Date, nullable=False, comment='统计日期')
    total_count = db.Column(db.Integer, nullable=False, default=0, comment='截至当日总数')
    daily_count = db.Column(db.Integer, nullable=False, default=0, comment='当日新增数')
    channel_stats = db.Column(db.JSON, comment='按渠道统计的JSON数据')
    unit_stats = db.Column(db.<PERSON><PERSON><PERSON>, comment='按单位统计的JSON数据')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')

    # 添加唯一索引确保每天只有一条记录
    __table_args__ = (
        db.UniqueConstraint('stat_date', name='uk_stat_date'),
        db.Index('idx_stat_date', 'stat_date'),
    )

    def to_json(self):
        """转换为JSON格式"""
        return {
            'id': self.id,
            'stat_date': self.stat_date.strftime('%Y-%m-%d') if self.stat_date else None,
            'total_count': self.total_count,
            'daily_count': self.daily_count,
            'channel_stats': self.channel_stats,
            'unit_stats': self.unit_stats,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None
        }


class FxChannelStatistics(db.Model):
    """
    渠道统计表
    用于存储各个渠道的统计信息
    """
    __tablename__ = 'fx_channel_statistics'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    channel_name = db.Column(db.String(50), nullable=False, comment='渠道名称')
    stat_date = db.Column(db.Date, nullable=False, comment='统计日期')
    article_count = db.Column(db.Integer, nullable=False, default=0, comment='文章数量')
    total_views = db.Column(db.BigInteger, nullable=False, default=0, comment='总浏览量')
    total_likes = db.Column(db.BigInteger, nullable=False, default=0, comment='总点赞量')
    total_comments = db.Column(db.BigInteger, nullable=False, default=0, comment='总评论量')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')

    # 添加复合唯一索引
    __table_args__ = (
        db.UniqueConstraint('channel_name', 'stat_date', name='uk_channel_stat_date'),
        db.Index('idx_channel_stat_date', 'channel_name', 'stat_date'),
    )

    def to_json(self):
        """转换为JSON格式"""
        return {
            'id': self.id,
            'channel_name': self.channel_name,
            'stat_date': self.stat_date.strftime('%Y-%m-%d') if self.stat_date else None,
            'article_count': self.article_count,
            'total_views': self.total_views,
            'total_likes': self.total_likes,
            'total_comments': self.total_comments,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None
        }


class FxUnitStatistics(db.Model):
    """
    单位统计表
    用于存储各个单位的统计信息
    """
    __tablename__ = 'fx_unit_statistics'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    unit_name = db.Column(db.String(100), nullable=False, comment='单位名称')
    stat_date = db.Column(db.Date, nullable=False, comment='统计日期')
    article_count = db.Column(db.Integer, nullable=False, default=0, comment='文章数量')
    total_views = db.Column(db.BigInteger, nullable=False, default=0, comment='总浏览量')
    total_likes = db.Column(db.BigInteger, nullable=False, default=0, comment='总点赞量')
    total_comments = db.Column(db.BigInteger, nullable=False, default=0, comment='总评论量')
    avg_views = db.Column(db.Float, nullable=True, comment='平均浏览量')
    max_views = db.Column(db.Integer, nullable=True, comment='最高浏览量')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')

    # 添加复合唯一索引
    __table_args__ = (
        db.UniqueConstraint('unit_name', 'stat_date', name='uk_unit_stat_date'),
        db.Index('idx_unit_stat_date', 'unit_name', 'stat_date'),
    )

    def to_json(self):
        """转换为JSON格式"""
        return {
            'id': self.id,
            'unit_name': self.unit_name,
            'stat_date': self.stat_date.strftime('%Y-%m-%d') if self.stat_date else None,
            'article_count': self.article_count,
            'total_views': self.total_views,
            'total_likes': self.total_likes,
            'total_comments': self.total_comments,
            'avg_views': self.avg_views,
            'max_views': self.max_views,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None
        }
