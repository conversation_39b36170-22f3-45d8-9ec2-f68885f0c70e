# 普法工作管理系统统计接口说明

## 概述

本模块为普法工作管理系统提供统计功能，主要用于统计平台收集官方信息的总数和今日增加数等相关数据。

## 数据库表结构

### 1. fx_article_records (文章记录表)
- 存储平台收集的所有官方信息文章
- 主要字段：文章标题、内容、发布时间、浏览量、点赞量、评论量等

### 2. fx_info_statistics (信息统计表)
- 存储每日统计汇总数据
- 主要字段：统计日期、总数、当日新增数、渠道统计、单位统计

### 3. fx_channel_statistics (渠道统计表)
- 存储各渠道的详细统计数据
- 主要字段：渠道名称、文章数量、总浏览量、总点赞量、总评论量

### 4. fx_unit_statistics (单位统计表)
- 存储各单位的详细统计数据
- 主要字段：单位名称、文章数量、总浏览量、平均浏览量、最高浏览量

## API接口

### 1. 获取信息汇总统计
**接口地址：** `GET /api/faxuan/statistics/info_summary`

**功能描述：** 获取平台收集官方信息总数和今日增加数

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "total_count": 1250,
        "today_count": 25,
        "today_date": "2025-08-05"
    }
}
```

### 2. 获取信息详细统计
**接口地址：** `GET /api/faxuan/statistics/info_detail`

**功能描述：** 获取平台收集官方信息的详细统计，支持按渠道、单位等维度筛选

**请求参数：**
- `date` (可选): 查询日期，格式YYYY-MM-DD，不传则查询今日
- `channel` (可选): 爬取渠道筛选
- `unit_name` (可选): 单位名称筛选

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "query_date": "2025-08-05",
        "total_count": 25,
        "channel_stats": [
            {
                "channel": "微信公众号",
                "count": 15
            },
            {
                "channel": "官网",
                "count": 10
            }
        ],
        "unit_stats": [
            {
                "unit_name": "市法院",
                "count": 8
            },
            {
                "unit_name": "市教育局",
                "count": 7
            }
        ]
    }
}
```

### 3. 获取信息趋势统计
**接口地址：** `GET /api/faxuan/statistics/trend`

**功能描述：** 获取最近N天的信息收集趋势统计

**请求参数：**
- `days` (可选): 查询天数，默认7天，最大365天

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "days": 7,
        "start_date": "2025-07-30",
        "end_date": "2025-08-05",
        "total_count": 175,
        "avg_count": 25.0,
        "trend_data": [
            {
                "date": "2025-07-30",
                "count": 20
            },
            {
                "date": "2025-07-31",
                "count": 25
            },
            {
                "date": "2025-08-01",
                "count": 30
            },
            {
                "date": "2025-08-02",
                "count": 22
            },
            {
                "date": "2025-08-03",
                "count": 28
            },
            {
                "date": "2025-08-04",
                "count": 25
            },
            {
                "date": "2025-08-05",
                "count": 25
            }
        ]
    }
}
```

## 数据库初始化

1. 执行SQL脚本创建统计表：
```bash
mysql -u username -p database_name < web/models/faxuan/fx_statistics_tables.sql
```

2. 或者通过Flask应用自动创建表：
```python
from web import create_app, db
app = create_app('dev')
with app.app_context():
    db.create_all()
```

## 定时任务

系统提供了定时任务来自动更新统计数据，提高查询性能：

### 使用方法

1. **更新昨天的统计数据：**
```python
from web.task.fx_statistics_task import update_daily_statistics
from datetime import date, timedelta

yesterday = date.today() - timedelta(days=1)
update_daily_statistics(yesterday)
```

2. **更新当天的统计数据：**
```python
from web.task.fx_statistics_task import update_current_day_statistics
update_current_day_statistics()
```

3. **批量更新历史数据：**
```python
from web.task.fx_statistics_task import batch_update_statistics
from datetime import date, timedelta

start_date = date.today() - timedelta(days=30)
end_date = date.today()
batch_update_statistics(start_date, end_date)
```

### 建议的定时任务配置

- **每日凌晨2点**：更新昨天的统计数据
- **每小时**：更新当天的实时统计数据（可选）

## 注意事项

1. 所有接口都需要登录认证（`@login_required`装饰器）
2. 统计数据基于`fx_article_records`表的`create_time`字段
3. 为提高性能，建议定期运行统计任务更新汇总表
4. 日期格式统一使用`YYYY-MM-DD`
5. 所有数值字段在数据库中为NULL时，接口返回0

## 错误处理

接口统一返回格式：
- 成功：`{"code": 200, "msg": "操作成功", "data": {...}}`
- 失败：`{"code": 500, "msg": "错误信息", "data": null}`

常见错误码：
- 400：请求参数错误
- 401：未登录或认证失败
- 500：服务器内部错误
