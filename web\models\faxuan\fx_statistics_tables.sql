-- 普法工作管理系统统计相关数据表创建脚本
-- 用于创建统计功能所需的数据库表

-- 创建普法信息统计表
DROP TABLE IF EXISTS `fx_info_statistics`;
CREATE TABLE `fx_info_statistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `total_count` int NOT NULL DEFAULT '0' COMMENT '截至当日总数',
  `daily_count` int NOT NULL DEFAULT '0' COMMENT '当日新增数',
  `channel_stats` json DEFAULT NULL COMMENT '按渠道统计的JSON数据',
  `unit_stats` json DEFAULT NULL COMMENT '按单位统计的JSON数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_stat_date` (`stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='普法信息统计表' ROW_FORMAT=DYNAMIC;

-- 创建渠道统计表
DROP TABLE IF EXISTS `fx_channel_statistics`;
CREATE TABLE `fx_channel_statistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `channel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '渠道名称',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `article_count` int NOT NULL DEFAULT '0' COMMENT '文章数量',
  `total_views` bigint NOT NULL DEFAULT '0' COMMENT '总浏览量',
  `total_likes` bigint NOT NULL DEFAULT '0' COMMENT '总点赞量',
  `total_comments` bigint NOT NULL DEFAULT '0' COMMENT '总评论量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_channel_stat_date` (`channel_name`, `stat_date`),
  KEY `idx_channel_stat_date` (`channel_name`, `stat_date`)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='渠道统计表' ROW_FORMAT=DYNAMIC;

-- 创建单位统计表
DROP TABLE IF EXISTS `fx_unit_statistics`;
CREATE TABLE `fx_unit_statistics` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `unit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位名称',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `article_count` int NOT NULL DEFAULT '0' COMMENT '文章数量',
  `total_views` bigint NOT NULL DEFAULT '0' COMMENT '总浏览量',
  `total_likes` bigint NOT NULL DEFAULT '0' COMMENT '总点赞量',
  `total_comments` bigint NOT NULL DEFAULT '0' COMMENT '总评论量',
  `avg_views` float DEFAULT NULL COMMENT '平均浏览量',
  `max_views` int DEFAULT NULL COMMENT '最高浏览量',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_unit_stat_date` (`unit_name`, `stat_date`),
  KEY `idx_unit_stat_date` (`unit_name`, `stat_date`)
) ENGINE=InnoDB CHARACTER SET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='单位统计表' ROW_FORMAT=DYNAMIC;

-- 插入一些示例数据（可选）
-- 注意：以下INSERT语句包含子查询，如果执行出错，可以跳过这部分，只创建表结构即可

-- 插入今日统计数据示例（简化版本，避免子查询语法问题）
INSERT INTO `fx_info_statistics` (`stat_date`, `total_count`, `daily_count`, `channel_stats`, `unit_stats`)
VALUES
(CURDATE(), 0, 0, '{"微信公众号": 15, "官网": 8, "其他": 2}', '{"市法院": 10, "市教育局": 8, "玄武区司法局": 7}');

-- 如果需要插入实际统计数据，请在应用启动后使用以下语句：
-- UPDATE fx_info_statistics SET
--   total_count = (SELECT COUNT(*) FROM fx_article_records),
--   daily_count = (SELECT COUNT(*) FROM fx_article_records WHERE DATE(create_time) = CURDATE())
-- WHERE stat_date = CURDATE();
