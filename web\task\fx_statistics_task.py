# 普法工作管理系统统计数据定时任务
# 用于定时更新统计数据，提高查询性能

from datetime import datetime, date, timedelta
from sqlalchemy import func, and_
from web import db
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web.models.faxuan.FXStatistics import FxInfoStatistics, FxChannelStatistics, FxUnitStatistics
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def update_daily_statistics(target_date=None):
    """
    更新指定日期的统计数据
    
    Args:
        target_date: 目标日期，默认为昨天
    """
    if target_date is None:
        target_date = date.today() - timedelta(days=1)
    
    try:
        logger.info(f"开始更新 {target_date} 的统计数据")
        
        # 更新基础统计信息
        update_info_statistics(target_date)
        
        # 更新渠道统计信息
        update_channel_statistics(target_date)
        
        # 更新单位统计信息
        update_unit_statistics(target_date)
        
        db.session.commit()
        logger.info(f"成功更新 {target_date} 的统计数据")
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新 {target_date} 统计数据失败: {str(e)}")
        raise


def update_info_statistics(target_date):
    """
    更新基础信息统计
    """
    target_start = datetime.combine(target_date, datetime.min.time())
    target_end = datetime.combine(target_date, datetime.max.time())
    
    # 计算截至当日的总数
    total_count = db.session.query(func.count(FxArticleRecords.id)).filter(
        FxArticleRecords.create_time <= target_end
    ).scalar() or 0
    
    # 计算当日新增数
    daily_count = db.session.query(func.count(FxArticleRecords.id)).filter(
        and_(
            FxArticleRecords.create_time >= target_start,
            FxArticleRecords.create_time <= target_end
        )
    ).scalar() or 0
    
    # 计算按渠道统计
    channel_stats_raw = db.session.query(
        FxArticleRecords.crawl_channel,
        func.count(FxArticleRecords.id).label('count')
    ).filter(
        and_(
            FxArticleRecords.create_time >= target_start,
            FxArticleRecords.create_time <= target_end
        )
    ).group_by(FxArticleRecords.crawl_channel).all()
    
    channel_stats = {stat.crawl_channel: stat.count for stat in channel_stats_raw}
    
    # 计算按单位统计（取前10名）
    unit_stats_raw = db.session.query(
        FxArticleRecords.unit_name,
        func.count(FxArticleRecords.id).label('count')
    ).filter(
        and_(
            FxArticleRecords.create_time >= target_start,
            FxArticleRecords.create_time <= target_end
        )
    ).group_by(FxArticleRecords.unit_name).order_by(
        func.count(FxArticleRecords.id).desc()
    ).limit(10).all()
    
    unit_stats = {stat.unit_name: stat.count for stat in unit_stats_raw}
    
    # 查找是否已存在记录
    existing_record = db.session.query(FxInfoStatistics).filter(
        FxInfoStatistics.stat_date == target_date
    ).first()
    
    if existing_record:
        # 更新现有记录
        existing_record.total_count = total_count
        existing_record.daily_count = daily_count
        existing_record.channel_stats = channel_stats
        existing_record.unit_stats = unit_stats
        existing_record.update_time = datetime.now()
    else:
        # 创建新记录
        new_record = FxInfoStatistics(
            stat_date=target_date,
            total_count=total_count,
            daily_count=daily_count,
            channel_stats=channel_stats,
            unit_stats=unit_stats
        )
        db.session.add(new_record)


def update_channel_statistics(target_date):
    """
    更新渠道统计信息
    """
    target_start = datetime.combine(target_date, datetime.min.time())
    target_end = datetime.combine(target_date, datetime.max.time())
    
    # 查询各渠道的统计数据
    channel_stats = db.session.query(
        FxArticleRecords.crawl_channel,
        func.count(FxArticleRecords.id).label('article_count'),
        func.coalesce(func.sum(FxArticleRecords.view_count), 0).label('total_views'),
        func.coalesce(func.sum(FxArticleRecords.likes), 0).label('total_likes'),
        func.coalesce(func.sum(FxArticleRecords.comments), 0).label('total_comments')
    ).filter(
        and_(
            FxArticleRecords.create_time >= target_start,
            FxArticleRecords.create_time <= target_end
        )
    ).group_by(FxArticleRecords.crawl_channel).all()
    
    for stat in channel_stats:
        # 查找是否已存在记录
        existing_record = db.session.query(FxChannelStatistics).filter(
            and_(
                FxChannelStatistics.channel_name == stat.crawl_channel,
                FxChannelStatistics.stat_date == target_date
            )
        ).first()
        
        if existing_record:
            # 更新现有记录
            existing_record.article_count = stat.article_count
            existing_record.total_views = stat.total_views
            existing_record.total_likes = stat.total_likes
            existing_record.total_comments = stat.total_comments
            existing_record.update_time = datetime.now()
        else:
            # 创建新记录
            new_record = FxChannelStatistics(
                channel_name=stat.crawl_channel,
                stat_date=target_date,
                article_count=stat.article_count,
                total_views=stat.total_views,
                total_likes=stat.total_likes,
                total_comments=stat.total_comments
            )
            db.session.add(new_record)


def update_unit_statistics(target_date):
    """
    更新单位统计信息
    """
    target_start = datetime.combine(target_date, datetime.min.time())
    target_end = datetime.combine(target_date, datetime.max.time())
    
    # 查询各单位的统计数据
    unit_stats = db.session.query(
        FxArticleRecords.unit_name,
        func.count(FxArticleRecords.id).label('article_count'),
        func.coalesce(func.sum(FxArticleRecords.view_count), 0).label('total_views'),
        func.coalesce(func.sum(FxArticleRecords.likes), 0).label('total_likes'),
        func.coalesce(func.sum(FxArticleRecords.comments), 0).label('total_comments'),
        func.coalesce(func.avg(FxArticleRecords.view_count), 0).label('avg_views'),
        func.coalesce(func.max(FxArticleRecords.view_count), 0).label('max_views')
    ).filter(
        and_(
            FxArticleRecords.create_time >= target_start,
            FxArticleRecords.create_time <= target_end
        )
    ).group_by(FxArticleRecords.unit_name).all()
    
    for stat in unit_stats:
        # 查找是否已存在记录
        existing_record = db.session.query(FxUnitStatistics).filter(
            and_(
                FxUnitStatistics.unit_name == stat.unit_name,
                FxUnitStatistics.stat_date == target_date
            )
        ).first()
        
        if existing_record:
            # 更新现有记录
            existing_record.article_count = stat.article_count
            existing_record.total_views = stat.total_views
            existing_record.total_likes = stat.total_likes
            existing_record.total_comments = stat.total_comments
            existing_record.avg_views = float(stat.avg_views) if stat.avg_views else 0.0
            existing_record.max_views = stat.max_views
            existing_record.update_time = datetime.now()
        else:
            # 创建新记录
            new_record = FxUnitStatistics(
                unit_name=stat.unit_name,
                stat_date=target_date,
                article_count=stat.article_count,
                total_views=stat.total_views,
                total_likes=stat.total_likes,
                total_comments=stat.total_comments,
                avg_views=float(stat.avg_views) if stat.avg_views else 0.0,
                max_views=stat.max_views
            )
            db.session.add(new_record)


def update_current_day_statistics():
    """
    更新当天的统计数据（实时统计）
    """
    today = date.today()
    update_daily_statistics(today)


def batch_update_statistics(start_date, end_date):
    """
    批量更新指定日期范围的统计数据
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
    """
    current_date = start_date
    while current_date <= end_date:
        try:
            update_daily_statistics(current_date)
            logger.info(f"成功更新 {current_date} 的统计数据")
        except Exception as e:
            logger.error(f"更新 {current_date} 统计数据失败: {str(e)}")
        
        current_date += timedelta(days=1)


if __name__ == "__main__":
    # 测试用：更新昨天的统计数据
    from web import create_app
    import os
    
    app = create_app(os.getenv('FLASK_CONFIG') or 'dev')
    with app.app_context():
        update_daily_statistics()
        print("统计数据更新完成")
